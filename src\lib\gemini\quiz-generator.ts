import { generateWithGeminiStructured, SchemaConfig, Type, getChatSession, sendStructuredChatMessage, ChatMessage } from './client';
import { AIGeneratedQuiz, AICourseOutline, AIModuleOutline, AIGenerationError } from './types';

// Schema for AIGeneratedQuiz structured output
const quizSchema: SchemaConfig = {
  responseMimeType: 'application/json',
  responseSchema: {
    type: Type.OBJECT,
    properties: {
      name: {
        type: Type.STRING,
        description: 'Quiz title'
      },
      description: {
        type: Type.STRING,
        description: 'Quiz description explaining what it covers'
      },
      timeLimit: {
        type: Type.NUMBER,
        description: 'Time limit in minutes'
      },
      minimumScore: {
        type: Type.NUMBER,
        description: 'Minimum score percentage'
      },
      questions: {
        type: Type.ARRAY,
        items: {
          type: Type.OBJECT,
          properties: {
            orderIndex: { type: Type.NUMBER },
            type: {
              type: Type.STRING,
              enum: ['multiple_choice', 'true_false', 'essay'],
              description: 'Question type'
            },
            question: {
              type: Type.ARRAY,
              items: {
                type: Type.OBJECT,
                properties: {
                  id: { type: Type.NUMBER },
                  type: { type: Type.STRING, enum: ['text'] },
                  value: { type: Type.STRING, description: 'Markdown line' }
                },
                required: ['id', 'type', 'value']
              }
            },
            options: {
              type: Type.ARRAY,
              items: {
                type: Type.OBJECT,
                properties: {
                  id: { type: Type.NUMBER },
                  isCorrect: { type: Type.BOOLEAN },
                  content: {
                    type: Type.ARRAY,
                    items: {
                      type: Type.OBJECT,
                      properties: {
                        id: { type: Type.NUMBER },
                        type: { type: Type.STRING, enum: ['text']},
                        value: { type: Type.STRING }
                      },
                      required: ['id', 'type', 'value']
                    }
                  }
                },
                required: ['id', 'isCorrect', 'content']
              }
            },
            essayAnswer: { type: Type.STRING },
            explanation: {
              type: Type.ARRAY,
              items: {
                type: Type.OBJECT,
                properties: {
                  id: { type: Type.NUMBER },
                  type: { type: Type.STRING, enum: ['text']},
                  value: { type: Type.STRING }
                },
                required: ['id', 'type', 'value']
              }
            },
            points: { type: Type.NUMBER }
          },
          required: ['orderIndex', 'type', 'question', 'points', 'essayAnswer', 'explanation']
        }
      }
    },
    required: ['name', 'description', 'timeLimit', 'minimumScore', 'questions']
  }
};

/**
 * Generate a module quiz that tests understanding of the entire module with chat session
 * @param moduleContext Module context
 * @param courseContext Course context
 * @param sessionId Chat session ID for maintaining context
 * @returns Promise<AIGeneratedQuiz> Generated module quiz
 */
export const generateModuleQuiz = async (
  moduleContext: AIModuleOutline,
  courseContext: AICourseOutline,
  sessionId: string
): Promise<AIGeneratedQuiz> => {
  try {
    const prompt = createModuleQuizPrompt(moduleContext, courseContext);
    
    // Create or get existing chat session
    getChatSession(sessionId);
    
    // Prepare message
    const message: ChatMessage = {
      role: 'user',
      parts: [{ text: prompt }]
    };

    const quiz = await sendStructuredChatMessage<AIGeneratedQuiz>(sessionId, message, quizSchema);
    
    // Validate the generated quiz
    validateGeneratedQuiz(quiz, 'module');
    
    return quiz;
  } catch (error) {
    if (error instanceof AIGenerationError) {
      throw error;
    }
    throw new AIGenerationError(
      `Failed to generate module quiz for: ${moduleContext.name}`,
      'QUIZ_GENERATION_ERROR',
      error
    );
  }
};

/**
 * Generate a module quiz that tests understanding of the entire module (backward compatibility)
 * @param moduleContext Module context
 * @param courseContext Course context
 * @returns Promise<AIGeneratedQuiz> Generated module quiz
 */
export const generateModuleQuizLegacy = async (
  moduleContext: AIModuleOutline,
  courseContext: AICourseOutline
): Promise<AIGeneratedQuiz> => {
  try {
    const prompt = createModuleQuizPrompt(moduleContext, courseContext);
    
    const contents = [
      {
        role: 'user',
        parts: [{ text: prompt }]
      }
    ];

    const quiz = await generateWithGeminiStructured<AIGeneratedQuiz>(contents, quizSchema);
    
    // Validate the generated quiz
    validateGeneratedQuiz(quiz, 'module');
    
    return quiz;
  } catch (error) {
    if (error instanceof AIGenerationError) {
      throw error;
    }
    throw new AIGenerationError(
      `Failed to generate module quiz for: ${moduleContext.name}`,
      'QUIZ_GENERATION_ERROR',
      error
    );
  }
};

/**
 * Generate a final exam that tests understanding of the entire course with chat session
 * @param courseContext Course context
 * @param sessionId Chat session ID for maintaining context
 * @returns Promise<AIGeneratedQuiz> Generated final exam
 */
export const generateFinalExam = async (
  courseContext: AICourseOutline,
  sessionId: string
): Promise<AIGeneratedQuiz> => {
  try {
    const prompt = createFinalExamPrompt(courseContext);
    
    // Create or get existing chat session
    getChatSession(sessionId);
    
    // Prepare message
    const message: ChatMessage = {
      role: 'user',
      parts: [{ text: prompt }]
    };

    const exam = await sendStructuredChatMessage<AIGeneratedQuiz>(sessionId, message, quizSchema);
    
    // Validate the generated exam
    validateGeneratedQuiz(exam, 'final');
    
    return exam;
  } catch (error) {
    if (error instanceof AIGenerationError) {
      throw error;
    }
    throw new AIGenerationError(
      `Failed to generate final exam for course: ${courseContext.courseName}`,
      'QUIZ_GENERATION_ERROR',
      error
    );
  }
};

/**
 * Generate a final exam that tests understanding of the entire course (backward compatibility)
 * @param courseContext Course context
 * @returns Promise<AIGeneratedQuiz> Generated final exam
 */
export const generateFinalExamLegacy = async (
  courseContext: AICourseOutline
): Promise<AIGeneratedQuiz> => {
  try {
    const prompt = createFinalExamPrompt(courseContext);
    
    const contents = [
      {
        role: 'user',
        parts: [{ text: prompt }]
      }
    ];

    const exam = await generateWithGeminiStructured<AIGeneratedQuiz>(contents, quizSchema);
    
    // Validate the generated exam
    validateGeneratedQuiz(exam, 'final');
    
    return exam;
  } catch (error) {
    if (error instanceof AIGenerationError) {
      throw error;
    }
    throw new AIGenerationError(
      `Failed to generate final exam for course: ${courseContext.courseName}`,
      'QUIZ_GENERATION_ERROR',
      error
    );
  }
};

/**
 * Generate a standalone chapter quiz with chat session
 * @param chapterName Chapter name
 * @param chapterDescription Chapter description
 * @param moduleContext Module context
 * @param courseContext Course context
 * @param sessionId Chat session ID for maintaining context
 * @returns Promise<AIGeneratedQuiz> Generated chapter quiz
 */
export const generateChapterQuiz = async (
  chapterName: string,
  chapterDescription: string,
  moduleContext: AIModuleOutline,
  courseContext: AICourseOutline,
  sessionId: string
): Promise<AIGeneratedQuiz> => {
  try {
    const prompt = createChapterQuizPrompt(chapterName, chapterDescription, moduleContext, courseContext);
    
    // Create or get existing chat session
    getChatSession(sessionId);
    
    // Prepare message
    const message: ChatMessage = {
      role: 'user',
      parts: [{ text: prompt }]
    };

    const quiz = await sendStructuredChatMessage<AIGeneratedQuiz>(sessionId, message, quizSchema);
    
    // Validate the generated quiz
    validateGeneratedQuiz(quiz, 'chapter');
    
    return quiz;
  } catch (error) {
    if (error instanceof AIGenerationError) {
      throw error;
    }
    throw new AIGenerationError(
      `Failed to generate chapter quiz for: ${chapterName}`,
      'QUIZ_GENERATION_ERROR',
      error
    );
  }
};

/**
 * Generate a standalone chapter quiz (backward compatibility)
 * @param chapterName Chapter name
 * @param chapterDescription Chapter description
 * @param moduleContext Module context
 * @param courseContext Course context
 * @returns Promise<AIGeneratedQuiz> Generated chapter quiz
 */
export const generateChapterQuizLegacy = async (
  chapterName: string,
  chapterDescription: string,
  moduleContext: AIModuleOutline,
  courseContext: AICourseOutline
): Promise<AIGeneratedQuiz> => {
  try {
    const prompt = createChapterQuizPrompt(chapterName, chapterDescription, moduleContext, courseContext);
    
    const contents = [
      {
        role: 'user',
        parts: [{ text: prompt }]
      }
    ];

    const quiz = await generateWithGeminiStructured<AIGeneratedQuiz>(contents, quizSchema);
    
    // Validate the generated quiz
    validateGeneratedQuiz(quiz, 'chapter');
    
    return quiz;
  } catch (error) {
    if (error instanceof AIGenerationError) {
      throw error;
    }
    throw new AIGenerationError(
      `Failed to generate chapter quiz for: ${chapterName}`,
      'QUIZ_GENERATION_ERROR',
      error
    );
  }
};

/**
 * Create prompt for module quiz generation
 * @param moduleContext Module context
 * @param courseContext Course context
 * @returns Formatted prompt string
 */
export const createModuleQuizPrompt = (
  moduleContext: AIModuleOutline,
  courseContext: AICourseOutline
): string => {
  const chaptersInfo = moduleContext.chapters
    .map(chapter => `- ${chapter.name}: ${chapter.description}`)
    .join('\n');

  return `
Generate a comprehensive module quiz that tests understanding of an entire module using the PDF content that i sent before.

Course Context:
- Course: "${courseContext.courseName}"
- Course Description: ${courseContext.description}

Module Context:
- Module: "${moduleContext.name}"
- Module Description: ${moduleContext.description}

Chapters in this module:
${chaptersInfo}

Generate a quiz with the following requirements:

Quiz Guidelines:
- The quiz MUST contain EXACTLY 20 questions. Do NOT generate more or fewer than 20 questions.
- Questions should cover key concepts from each chapter
- Mix question types: 60% multiple choice, 30% true/false, 10% essay
- Ensure questions are challenging but fair
- For multiple choice: provide 4 options with only one correct answer
- For true/false: make statements that are clearly true or false
- For essay: ask questions that require synthesis of module concepts
- Set time limit between 15-30 minutes
- Set minimum score between 70-80%
- Distribute points evenly across questions
- Total points should be 100


`;
};

/**
 * Create prompt for final exam generation
 * @param courseContext Course context
 * @returns Formatted prompt string
 */
export const createFinalExamPrompt = (courseContext: AICourseOutline): string => {
  const modulesInfo = courseContext.modules
    .map((module, index) => {
      const chaptersInfo = module.chapters
        .map(chapter => `    - ${chapter.name}`)
        .join('\n');
      return `Module ${index + 1}: ${module.name}\n${chaptersInfo}`;
    })
    .join('\n\n');

  return `
Generate a comprehensive final exam that tests understanding of the entire course using the PDF content that i sent before.

Course Context:
- Course: "${courseContext.courseName}"
- Course Description: ${courseContext.description}

Course Structure:
${modulesInfo}

Generate a comprehensive final exam with the following requirements:

Exam Guidelines:
- The exam MUST contain EXACTLY 40 questions. Do NOT generate more or fewer than 40 questions.
- Questions should cover key concepts from all modules proportionally
- Mix question types: 50% multiple choice, 30% true/false, 20% essay
- Include questions that require synthesis across multiple modules
- For multiple choice: provide 4 options with only one correct answer
- For true/false: make statements that are clearly true or false
- For essay: ask comprehensive questions that demonstrate mastery
- Set time limit between 45-90 minutes
- Set minimum score between 75-85%
- Distribute points to emphasize important concepts
- Total points should be 100
- Include some challenging questions that test deep understanding


`;
};

/**
 * Create prompt for chapter quiz generation
 * @param chapterName Chapter name
 * @param chapterDescription Chapter description
 * @param moduleContext Module context
 * @param courseContext Course context
 * @returns Formatted prompt string
 */
export const createChapterQuizPrompt = (
  chapterName: string,
  chapterDescription: string,
  moduleContext: AIModuleOutline,
  courseContext: AICourseOutline
): string => {
  return `
Generate a focused chapter quiz that tests understanding of a specific chapter.

Course Context:
- Course: "${courseContext.courseName}"
- Module: "${moduleContext.name}"

Chapter Context:
- Chapter: "${chapterName}"
- Chapter Description: ${chapterDescription}

Generate a focused chapter quiz with the following requirements:

Quiz Guidelines:
- Create 3-7 questions focused specifically on this chapter
- Questions should test key concepts and understanding from the chapter
- Mix question types: 70% multiple choice, 20% true/false, 10% essay
- For multiple choice: provide 4 options with only one correct answer
- For true/false: make statements that are clearly true or false
- For essay: ask questions that require understanding of chapter concepts
- Set time limit between 5-15 minutes
- Set minimum score between 70-80%
- Distribute points evenly across questions
- Total points should be 100


`;
};

/**
 * Validate generated quiz
 * @param quiz Generated quiz to validate
 * @param quizType Type of quiz for context
 * @throws AIGenerationError if validation fails
 */
export const validateGeneratedQuiz = (quiz: AIGeneratedQuiz, quizType: 'chapter' | 'module' | 'final'): void => {
  if (!quiz.name || typeof quiz.name !== 'string') {
    throw new AIGenerationError(
      `Invalid ${quizType} quiz: missing or invalid name`,
      'VALIDATION_ERROR'
    );
  }

  if (!quiz.description || typeof quiz.description !== 'string') {
    throw new AIGenerationError(
      `Invalid ${quizType} quiz: missing or invalid description`,
      'VALIDATION_ERROR'
    );
  }

  if (typeof quiz.timeLimit !== 'number' || quiz.timeLimit <= 0) {
    throw new AIGenerationError(
      `Invalid ${quizType} quiz: timeLimit must be a positive number`,
      'VALIDATION_ERROR'
    );
  }

  // Validate time limits based on quiz type
  const timeLimits = {
    chapter: { min: 5, max: 15 },
    module: { min: 15, max: 30 },
    final: { min: 45, max: 90 }
  };
  
  const { min, max } = timeLimits[quizType];
  if (quiz.timeLimit < min || quiz.timeLimit > max) {
    throw new AIGenerationError(
      `Invalid ${quizType} quiz: timeLimit should be between ${min}-${max} minutes`,
      'VALIDATION_ERROR'
    );
  }

  if (typeof quiz.minimumScore !== 'number' || quiz.minimumScore < 0 || quiz.minimumScore > 100) {
    throw new AIGenerationError(
      `Invalid ${quizType} quiz: minimumScore must be between 0 and 100`,
      'VALIDATION_ERROR'
    );
  }

  if (!Array.isArray(quiz.questions) || quiz.questions.length === 0) {
    throw new AIGenerationError(
      `Invalid ${quizType} quiz: questions must be a non-empty array`,
      'VALIDATION_ERROR'
    );
  }

  // Validate question count based on quiz type
  const questionCounts = {
    chapter: { min: 3, max: 10 },
    module: { min: 20, max: 20 },
    final: { min: 40, max: 40 }
  };
  
  const { min: minQ, max: maxQ } = questionCounts[quizType];
  if (quiz.questions.length < minQ || quiz.questions.length > maxQ) {
    throw new AIGenerationError(
      `Invalid ${quizType} quiz: should have ${minQ} questions`,
      'VALIDATION_ERROR'
    );
  }

  quiz.questions.forEach((question, index) => {
    if (!['multiple_choice', 'true_false', 'essay'].includes(question.type)) {
      throw new AIGenerationError(
        `Invalid question at index ${index}: type must be multiple_choice, true_false, or essay`,
        'VALIDATION_ERROR'
      );
    }

    if (!Array.isArray(question.question) || question.question.length === 0) {
      throw new AIGenerationError(
        `Invalid question at index ${index}: missing or invalid question text`,
        'VALIDATION_ERROR'
      );
    }

    if (question.type === 'multiple_choice') {
      if (!Array.isArray(question.options) || question.options.length < 2) {
        throw new AIGenerationError(
          `Invalid question at index ${index}: multiple choice questions must have at least 2 options`,
          'VALIDATION_ERROR'
        );
      }
    }

    // Validation for essay questions
    if (question.type === 'essay' && (!question.essayAnswer || typeof question.essayAnswer !== 'string')) {
      throw new AIGenerationError(
        `Invalid question at index ${index}: essay questions must have essayAnswer`,
        'VALIDATION_ERROR'
      );
    }
  });
};